package com.ruoyi.domain.basicData;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 单据物料信息详情
 */
@Data
@TableName("document_inventory_detail")
public class DocumentInventoryDetail {

    /**
     * 主键编码
     */
    private String id;

    /**
     * 出入库单据编码
     */
    private String documentCode;
    /**
     * 出入库单据业务类型
     * 1:生产领料 2:生产补料 3:生产入库（成品入库） 4:生产退料 5采购入库  6采购退货出库  7销售出库  8销售退货入库
     */
    private String businessType;

    /**
     * 出入库单据详情编码
     * --对于具体物料
     */
    private String detailCode;

    /**
     * 容器编码
     */
    private String containerCode;

    /**
     * 批次库存ID - 关联 BasicMaterialBatchInventory表的主键
     */
    private String inventoryId;
    /**
     * 批次号
     */
    private String batchCode;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 单据类型：0-入库，1-出库
     * 用于区分该批次记录是入库还是出库操作
     */
    private Integer transactionType;

    /**
     * 容器批次计划数量
     */
    private Integer quantity;

    /**
     * 已完成数量（实际出入库完成的数量）
     */
    private Integer completedNum;

    /**
     * 质检状态：
     * 0-无需质检（物料本身不需要质检）
     * 1-待质检（需要质检，等待开始质检）
     * 2-质检中（质检任务进行中）
     * 3-质检合格（质检通过，可进入下一环节）
     * 4-质检不合格（质检失败，需要处理）
     * 5-免检（有质检任务但质检结果为免检）
     */
    private Integer qcStatus;

    /**
     * 是否让步接收：0-否，1-是
     */
    private Integer isConcession;

    /**
     * 出入库状态：
     * 0-待确认（初始状态：等待确认备货/来料）
     * 1-待出入库（有确认数量：入库为待入库，出库为待出库，支持分批）
     * 2-出入库中（正在进行出入库操作：入库为入库中，出库为出库中）
     * 3-待生产确认（仅生产相关出库单据：SCLL、SCBL、SCRK、SCTL）
     * 4-生产已确认（生产确认完成）
     * 5-待仓库确认（等待仓库最终确认）
     * 6-仓库已确认（仓库确认完成）
     * 7-已完成（最终完成状态）
     */
    private Integer warehouseStatus;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

}